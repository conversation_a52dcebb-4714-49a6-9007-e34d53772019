import 'package:get/get.dart';

class FormValidation {
  // Validasi email
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email tidak boleh kosong';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'error_email_not_valid_str'.tr;
    }

    return null;
  }

  // Validasi nomor HP (hanya angka)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nomor HP tidak boleh kosong';
    }

    final phoneRegex = RegExp(r'^[0-9]+$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Nomor HP hanya boleh berisi angka';
    }

    if (value.length < 8 || value.length > 11) {
      return 'Nomor HP harus 10-13 digit';
    }

    return null;
  }

  // Validasi field wajib
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName tidak boleh kosong';
    }
    return null;
  }

  // Validasi NIK (16 digit angka)
  static String? validateNIK(String? value) {
    if (value == null || value.isEmpty) {
      return 'NIK tidak boleh kosong';
    }

    final nikRegex = RegExp(r'^[0-9]{16}$');
    if (!nikRegex.hasMatch(value)) {
      return 'NIK harus 16 digit angka';
    }

    return null;
  }

  // Validasi tanggal (1-31)
  static String? validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tanggal tidak boleh kosong';
    }

    final date = int.tryParse(value);
    if (date == null || date < 1 || date > 31) {
      return 'Tanggal harus antara 1-31';
    }

    final dateRegex = RegExp(r'^[0-9]{2}$');
    if (!dateRegex.hasMatch(value)) {
      return 'Tanggal harus 2 digit angka';
    }

    return null;
  }

  // Validasi tahun (4 digit)
  static String? validateYear(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tahun tidak boleh kosong';
    }

    final yearRegex = RegExp(r'^[0-9]{4}$');
    if (!yearRegex.hasMatch(value)) {
      return 'Tahun harus 4 digit';
    }

    final year = int.parse(value);
    final currentYear = DateTime.now().year;
    if (year < 1900 || year > currentYear) {
      return 'Tahun tidak valid';
    }

    return null;
  }

  // Validasi nomor rekening
  static String? validateAccountNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nomor rekening tidak boleh kosong';
    }

    final accountRegex = RegExp(r'^[0-9]+$');
    if (!accountRegex.hasMatch(value)) {
      return 'Nomor rekening hanya boleh berisi angka';
    }

    if (value.length < 8 || value.length > 20) {
      return 'Nomor rekening harus 8-20 digit';
    }

    return null;
  }

  // Validasi RT/RW (1-3 digit angka)
  static String? validateRtRw(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName tidak boleh kosong';
    }

    final rtRwRegex = RegExp(r'^[0-9]{1,3}$');
    if (!rtRwRegex.hasMatch(value)) {
      return '$fieldName harus 1-3 digit angka';
    }

    return null;
  }
}
