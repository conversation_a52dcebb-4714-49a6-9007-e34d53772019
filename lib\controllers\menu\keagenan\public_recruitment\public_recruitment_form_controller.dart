import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_self_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_terms_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_verification_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/public_api.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class PublicRecruitmentFormController extends BaseControllers {
  PageController pageController = PageController();
  RxInt activePage = 0.obs;

  RxBool isVerificationEmailSent = false.obs;
  RxBool isReadyToSubmit = false.obs;

  late SharedPreferences prefs;

  PublicApi publicApi = PublicApi();

  // Form ID untuk Firestore
  RxString formId = ''.obs;
  bool hasServerUuid = false;

  // Status form
  RxBool isFormLoaded = false.obs;
  RxBool isFormSaving = false.obs;
  RxBool isFormSubmitted = false.obs;
  RxString formStatus = 'draft'.obs;

  RxBool isWebInternetAvailable = false.obs;

  // Timer untuk auto-save
  Timer? _autoSaveTimer;
  final int _autoSaveIntervalSeconds = 5; // Auto-save setiap 5 detik

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // Instance untuk mencegah screenshot
  final NoScreenshot _noScreenshot = NoScreenshot.instance;

  // Sub-controllers
  late PublicFormVerificationController verificationController;
  late PublicFormIdentificationController identificationController;
  late PublicFormSelfIdentificationController selfIdentificationController;
  late PublicFormTermsController termsController;

  TextEditingController notesTextController = TextEditingController();

  RxBool isReady = false.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    // Disable screenshot untuk keamanan form recruitment
    await _disableScreenshot();

    // Initialize sub-controllers
    verificationController = Get.put(
      PublicFormVerificationController(baseController: this),
    );
    identificationController = Get.put(
      PublicFormIdentificationController(baseController: this),
    );
    selfIdentificationController = Get.put(
      PublicFormSelfIdentificationController(baseController: this),
    );
    _setupDataFromQuery();
    // Inisialisasi form ID atau ambil dari parameter jika ada
    _initFormId();
    // Setup listener untuk perubahan form
    _setupFormChangeListeners();
    // Setup trim listeners untuk semua text fields
    _setupTrimListeners();
  }

  _setupDataFromQuery() {
    termsController = Get.put(PublicFormTermsController(baseController: this));
    verificationController.recruiterName.value = Uri.decodeComponent(
      Get.parameters['recruiterName'] ?? '',
    );
    verificationController.recruiterId.value = Uri.decodeComponent(
      Get.parameters['recruiterId'] ?? '',
    );
    verificationController.recruiterBranch.value = Uri.decodeComponent(
      Get.parameters['recruiterBranch'] ?? '',
    );
    verificationController.recruiterCode.value = Uri.decodeComponent(
      Get.parameters['recruiterCode'] ?? '',
    );
    verificationController.recruiterLevel.value = Uri.decodeComponent(
      Get.parameters['recruiterLevel'] ?? '',
    );
    verificationController.recruiterPhoto.value = Uri.decodeComponent(
      Get.parameters['recruiterPhoto'] ?? '',
    );
    verificationController.candidateLevelController.text = Uri.decodeComponent(
      Get.parameters['candidateLevelController'] ?? '',
    );
    verificationController.candidateBranchController.text = Uri.decodeComponent(
      Get.parameters['candidateBranchController'] ?? '',
    );
    verificationController.candidateBranchText.value = Uri.decodeComponent(
      Get.parameters['candidateBranchController'] ?? '',
    );
    verificationController.candidateBranchCode.value = int.parse(
      (Uri.decodeComponent(Get.parameters['candidateBranchCode'] ?? '0')),
    );
  }

  @override
  void onReady() {
    super.onReady();
    isReady.value = true;
  }

  // Delegate to sub-controllers for API responses
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == 1) {
      setLoading(false);
      validateCurrentPage(webInternetConnection: true);
      return;
    }

    // Handle submit recruitment form response
    if (requestCode == kReqSubmitRecruitmentForm) {
      _handleSubmitFormSuccess(response);
      return;
    }

    // Delegate to appropriate sub-controller
    verificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    selfIdentificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    termsController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    if (requestCode == 1) {
      setLoading(false);
      validateCurrentPage(webInternetConnection: false);
      return;
    }
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    Get.snackbar(
      'Error',
      'Terjadi kesalahan',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: kColorGlobalBgRed,
      colorText: kColorGlobalRed,
    );
  }

  // Handle submit form success response
  void _handleSubmitFormSuccess(response) async {
    log('Submit form success response: $response');

    // Update form status to submitted
    selfIdentificationController.isEmailVerified.value = true;
    isFormSubmitted.value = true;
    formStatus.value = 'submitted';
  }

  // Inisialisasi form ID
  Future<void> _initFormId() async {
    // Cek apakah ada form ID dari parameter
    if (Get.parameters.containsKey('formId')) {
      formId.value = Get.parameters['formId']!;
      log('Menggunakan form ID dari parameter: ${formId.value}');
      // Load form data jika ada
      _loadFormData();
    } else {
      // Buat form ID baru menggunakan UUID
      formId.value = const Uuid().v4();
      log('Membuat form ID baru: ${formId.value}');
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Setup listeners for sub-controllers to notify main controller
    // This will be handled by each sub-controller calling onFormChanged()
  }

  // Setup trim listeners untuk semua text fields
  void _setupTrimListeners() {
    // Delegate to sub-controllers to setup their trim listeners
    verificationController.setupTrimListeners();
    identificationController.setupTrimListeners();
    selfIdentificationController.setupTrimListeners();
    // termsController tidak memiliki text fields yang perlu di-trim
  }

  // Trim semua field sebelum submit
  void _trimAllFields() {
    // Trim identification fields
    identificationController.nikController.text =
        identificationController.nikController.text.trim();
    identificationController.namaKtpController.text =
        identificationController.namaKtpController.text.trim();
    identificationController.tempatLahirController.text =
        identificationController.tempatLahirController.text.trim();
    identificationController.tanggalLahirController.text =
        identificationController.tanggalLahirController.text.trim();
    identificationController.bulanLahirController.text =
        identificationController.bulanLahirController.text.trim();
    identificationController.tahunLahirController.text =
        identificationController.tahunLahirController.text.trim();
    identificationController.alamatKtpController.text =
        identificationController.alamatKtpController.text.trim();
    identificationController.rtKtpController.text =
        identificationController.rtKtpController.text.trim();
    identificationController.rwKtpController.text =
        identificationController.rwKtpController.text.trim();
    identificationController.provinsiKtpController.text =
        identificationController.provinsiKtpController.text.trim();
    identificationController.kabupatenKtpController.text =
        identificationController.kabupatenKtpController.text.trim();
    identificationController.kecamatanKtpController.text =
        identificationController.kecamatanKtpController.text.trim();
    identificationController.kelurahanKtpController.text =
        identificationController.kelurahanKtpController.text.trim();

    // Trim domisili fields
    identificationController.alamatDomisiliController.text =
        identificationController.alamatDomisiliController.text.trim();
    identificationController.rtDomisiliController.text =
        identificationController.rtDomisiliController.text.trim();
    identificationController.rwDomisiliController.text =
        identificationController.rwDomisiliController.text.trim();
    identificationController.provinsiDomisiliController.text =
        identificationController.provinsiDomisiliController.text.trim();
    identificationController.kabupatenDomisiliController.text =
        identificationController.kabupatenDomisiliController.text.trim();
    identificationController.kecamatanDomisiliController.text =
        identificationController.kecamatanDomisiliController.text.trim();
    identificationController.kelurahanDomisiliController.text =
        identificationController.kelurahanDomisiliController.text.trim();

    // Trim self identification fields
    selfIdentificationController.emailController.text =
        selfIdentificationController.emailController.text.trim();
    selfIdentificationController.nomorHpController.text =
        selfIdentificationController.nomorHpController.text.trim();
    selfIdentificationController.pekerjaanController.text =
        selfIdentificationController.pekerjaanController.text.trim();
    selfIdentificationController.pekerjaanCodeController.text =
        selfIdentificationController.pekerjaanCodeController.text.trim();
    selfIdentificationController.emergencyNamaController.text =
        selfIdentificationController.emergencyNamaController.text.trim();
    selfIdentificationController.emergencyHubunganController.text =
        selfIdentificationController.emergencyHubunganController.text.trim();
    selfIdentificationController.emergencyNomorHpController.text =
        selfIdentificationController.emergencyNomorHpController.text.trim();
    selfIdentificationController.namaPemilikRekeningController.text =
        selfIdentificationController.namaPemilikRekeningController.text.trim();
    selfIdentificationController.nomorRekeningController.text =
        selfIdentificationController.nomorRekeningController.text.trim();
    selfIdentificationController.namaBankController.text =
        selfIdentificationController.namaBankController.text.trim();
  }

  // Handler ketika form berubah - called by sub-controllers
  void onFormChanged() {
    // Jika timer sudah berjalan, reset
    _autoSaveTimer?.cancel();

    // Mulai timer baru untuk auto-save
    _autoSaveTimer = Timer(Duration(seconds: _autoSaveIntervalSeconds), () {
      // Simpan form data
      saveFormData();
    });
  }

  // Load form data dari Firestore
  Future<void> _loadFormData() async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat memuat data form');
      return;
    }

    setLoading(true);

    try {
      final formData = await _firestoreService.getRecruitmentForm(formId.value);

      if (formData != null) {
        // Isi form dengan data yang ada
        _populateFormWithData(formData);
        isFormLoaded.value = true;
        log('Berhasil memuat data form dengan ID: ${formId.value}');
      } else {
        log('Tidak ada data form dengan ID: ${formId.value}');
      }
    } catch (e) {
      log('Error saat memuat data form: $e');
    } finally {
      setLoading(false);
    }
  }

  // Delegate form population to sub-controllers
  void _populateFormWithData(RecruitmentFormModel formData) {
    // Delegate to sub-controllers
    verificationController.populateFormData(formData);
    identificationController.populateFormData(formData);
    selfIdentificationController.populateFormData(formData);
    termsController.populateFormData(formData);

    // Set status form
    isFormSubmitted.value = formData.isSubmitted ?? false;
    formStatus.value = formData.formStatus ?? 'draft';

    hasServerUuid = formData.hasServerUuid ?? false;
  }

  // Simpan form data ke Firestore
  Future<bool> saveFormData({bool isSubmit = false}) async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat menyimpan data form');
      return false;
    }

    isFormSaving.value = true;

    try {
      // Jika ini adalah submit final, upload foto yang belum terupload
      if (isSubmit) {
        log('Final submit - checking and uploading pending photos...');
        final photosUploaded =
            await verificationController.uploadPendingPhotos();

        if (!photosUploaded) {
          log('Failed to upload pending photos');
          Get.snackbar(
            'Gagal',
            'Gagal mengupload foto. Silakan coba lagi.',
            colorText: Colors.white,
            backgroundColor: Colors.red,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        }
        log('All photos uploaded successfully');
      }

      // Buat model dari data form saat ini (dengan URL foto yang sudah terupdate)
      final formData = await _createFormModel(isSubmit: isSubmit);

      // Simpan ke Firestore
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
      );

      if (result) {
        log('Berhasil menyimpan data form dengan ID: ${formId.value}');
        if (isSubmit) {
          isFormSubmitted.value = true;
          formStatus.value = 'submitted';
        }
      } else {
        log('Gagal menyimpan data form dengan ID: ${formId.value}');
      }

      return result;
    } catch (e) {
      log('Error saat menyimpan data form: $e');
      return false;
    } finally {
      isFormSaving.value = false;
    }
  }

  // Buat model dari data form saat ini - delegate to sub-controllers
  Future<RecruitmentFormModel> _createFormModel({bool isSubmit = false}) async {
    return RecruitmentFormModel(
      id: formId.value,

      // Get data from sub-controllers
      recruiterName: verificationController.recruiterName.value,
      recruiterId: verificationController.recruiterId.value,
      recruiterBranch: verificationController.recruiterBranch.value,
      recruiterCode: verificationController.recruiterCode.value,
      recruiterLevel: verificationController.recruiterLevel.value,
      recruiterPhoto: verificationController.recruiterPhoto.value,
      candidateLevel: verificationController.candidateLevelController.text,
      candidateBranch: verificationController.candidateBranchController.text,
      candidateBranchCode: verificationController.candidateBranchCode.value,

      // Data FormIdentification
      nik: identificationController.nikController.text,
      namaKtp: identificationController.namaKtpController.text,
      tempatLahir: identificationController.tempatLahirController.text,
      tanggalLahir: identificationController.tanggalLahirController.text,
      bulanLahir: identificationController.bulanLahirController.text,
      tahunLahir: identificationController.tahunLahirController.text,
      jenisKelamin: identificationController.jenisKelaminController.text,
      alamatKtp: identificationController.alamatKtpController.text,
      rtKtp: identificationController.rtKtpController.text,
      rwKtp: identificationController.rwKtpController.text,
      provinsiKtp: identificationController.provinsiKtpController.text,
      kabupatenKtp: identificationController.kabupatenKtpController.text,
      kecamatanKtp: identificationController.kecamatanKtpController.text,
      kelurahanKtp: identificationController.kelurahanKtpController.text,
      maritalStatus: identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),

      // Data Alamat Domisili
      alamatDomisili: identificationController.alamatDomisiliController.text,
      rtDomisili: identificationController.rtDomisiliController.text,
      rwDomisili: identificationController.rwDomisiliController.text,
      provinsiDomisili:
          identificationController.provinsiDomisiliController.text,
      kabupatenDomisili:
          identificationController.kabupatenDomisiliController.text,
      kecamatanDomisili:
          identificationController.kecamatanDomisiliController.text,
      kelurahanDomisili:
          identificationController.kelurahanDomisiliController.text,
      isDomicileSameAsKtp:
          identificationController.selectedIsAddressSame.value == 0,

      // Data FormSelfIdentification
      email: selfIdentificationController.emailController.text,
      nomorHp: selfIdentificationController.nomorHpController.text,
      occupation: selfIdentificationController.pekerjaanController.text,
      occupationCode: selfIdentificationController.pekerjaanCodeController.text,
      emergencyNama: selfIdentificationController.emergencyNamaController.text,
      emergencyHubungan:
          selfIdentificationController.emergencyHubunganController.text,
      emergencyNomorHp:
          selfIdentificationController.emergencyNomorHpController.text,
      namaPemilikRekening:
          selfIdentificationController.namaPemilikRekeningController.text,
      nomorRekening: selfIdentificationController.nomorRekeningController.text,
      namaBank: selfIdentificationController.namaBankController.text,
      bankCode: selfIdentificationController.bankCode.value,

      // Data Foto - mobile only (web tidak perlu offline storage)
      ktpImagePath:
          !kIsWeb ? _getImagePath(verificationController.ktpImage.value) : null,
      selfieKtpImagePath:
          !kIsWeb
              ? _getImagePath(verificationController.selfieKtpImage.value)
              : null,
      pasFotoImagePath:
          !kIsWeb
              ? _getImagePath(verificationController.pasFotoImage.value)
              : null,

      // Data URL Foto
      ktpImageUrl: verificationController.ktpUrl.value,
      selfieKtpImageUrl: verificationController.selfieKtpUrl.value,
      pasFotoImageUrl: verificationController.pasFotoUrl.value,

      // Terms and Signature
      signature: termsController.signatureData.value,
      paraf: termsController.parafData.value,

      // Signature and Paraf URLs
      signatureUrl: termsController.signatureUrl.value,
      parafUrl: termsController.parafUrl.value,

      // Last job field
      lastJob: selfIdentificationController.lastJob.value,

      // New structured data fields
      last5YearJobData:
          selfIdentificationController.last5YearJobData
              .map((item) => item.toJson())
              .toList(),
      last2YearProductionData:
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      lastCompanyManPowerData:
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      rewardInfoData:
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),

      // Metadata
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      isSubmitted: isSubmit ? true : isFormSubmitted.value,
      formStatus: isSubmit ? 'submitted' : formStatus.value,
      hasServerUuid:
          false, // Default false, akan diupdate setelah mendapat UUID dari server
    );
  }

  // Helper method to get image path from either File or XFile
  String? _getImagePath(dynamic imageFile) {
    if (imageFile == null) return null;

    try {
      if (kIsWeb) {
        // On web, we can't store local paths, so we'll store as base64 or skip
        // For now, return null to avoid storing invalid paths
        return null;
      } else {
        // On mobile, get the path from File
        return imageFile.path;
      }
    } catch (e) {
      log('Error getting image path: $e');
      return null;
    }
  }

  Future<bool> submitForm() async {
    return await saveFormData(isSubmit: true);
  }

  // Update form ID setelah mendapat UUID dari server
  Future<bool> updateFormId(String newFormId) async {
    try {
      if (formId.value.isEmpty || newFormId.isEmpty) {
        log('Cannot update form ID: old or new ID is empty');
        return false;
      }

      if (formId.value == newFormId) {
        log('Form ID is already the same, no update needed');
        return true;
      }

      String oldFormId = formId.value;
      log('Updating form ID from $oldFormId to $newFormId');

      // Pindahkan data form dari ID lama ke ID baru di Firestore
      final moveResult = await _firestoreService.moveRecruitmentForm(
        oldFormId,
        newFormId,
      );

      if (moveResult) {
        // Update form ID di controller
        formId.value = newFormId;
        log('Successfully updated form ID to: $newFormId');
        return true;
      } else {
        log('Failed to move form data from $oldFormId to $newFormId');
        return false;
      }
    } catch (e) {
      log('Error updating form ID: $e');
      return false;
    }
  }

  getDataToSubmit() {
    // Trim semua field sebelum submit
    _trimAllFields();

    var data = {
      "recruiterCode": verificationController.recruiterCode.value,
      "ktpPhoto": verificationController.ktpUrl.value,
      "selfiePhoto": verificationController.selfieKtpUrl.value,
      "passPhoto": verificationController.pasFotoUrl.value,
      "positionLevel":
          verificationController.candidateLevelController.text.trim(),
      "branch": verificationController.candidateBranchCode.value,
      "bank": selfIdentificationController.bankCode.value,
      "nik": identificationController.nikController.text.trim(),
      "fullName": identificationController.namaKtpController.text.trim(),
      "birthPlace": identificationController.tempatLahirController.text.trim(),
      "birthDate":
          "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1)).toString().padLeft(2, '0')}-${identificationController.tanggalLahirController.text.padLeft(2, '0')}",
      "gender":
          identificationController.selectedJenisKelamin.value == 0 ? 'M' : 'F',
      "ktpProvince": identificationController.provinsiKtpController.text,
      "ktpCity": identificationController.kabupatenKtpController.text,
      "ktpDistrict": identificationController.kecamatanKtpController.text,
      "ktpSubDistrict": identificationController.kelurahanKtpController.text,
      "ktpRt": identificationController.rtKtpController.text,
      "ktpRw": identificationController.rwKtpController.text,
      "ktpAddress": identificationController.alamatKtpController.text,
      "isDomicileSameAsKtp":
          identificationController.selectedIsAddressSame.value == 0
              ? true
              : false,
      "domicileProvince":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.provinsiKtpController.text
              : identificationController.provinsiDomisiliController.text,
      "domicileCity":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kabupatenKtpController.text
              : identificationController.kabupatenDomisiliController.text,
      "domicileDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kecamatanKtpController.text
              : identificationController.kecamatanDomisiliController.text,
      "domicileSubDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kelurahanKtpController.text
              : identificationController.kelurahanDomisiliController.text,
      "domicileRt":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rtKtpController.text
              : identificationController.rtDomisiliController.text,
      "domicileRw":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rwKtpController.text
              : identificationController.rwDomisiliController.text,
      "domicileAddress":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.alamatKtpController.text
              : identificationController.alamatDomisiliController.text,
      "phoneNumber":
          selfIdentificationController.nomorHpController.text.startsWith('08')
              ? selfIdentificationController.nomorHpController.text
              : '08${selfIdentificationController.nomorHpController.text}',
      "maritalStatus": identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),
      "occupation": selfIdentificationController.pekerjaanController.text,
      "occupationCode":
          selfIdentificationController.pekerjaanCodeController.text,
      "email": selfIdentificationController.emailController.text,
      "emergencyContactName":
          selfIdentificationController.emergencyNamaController.text,
      "emergencyContactRelation":
          selfIdentificationController.emergencyHubunganController.text,
      "emergencyContactPhone":
          selfIdentificationController.emergencyNomorHpController.text
                  .startsWith('08')
              ? selfIdentificationController.emergencyNomorHpController.text
              : '08${selfIdentificationController.emergencyNomorHpController.text}',
      "bankAccountName":
          selfIdentificationController.namaPemilikRekeningController.text,
      "bankAccountNumber":
          selfIdentificationController.nomorRekeningController.text,
      "lastJob": selfIdentificationController.lastJob.value,
      "signature": termsController.signatureUrl.value,
      "paraf": termsController.parafUrl.value,
      "last5YearJobData": [
        for (
          int i = 0;
          i < selfIdentificationController.last5YearJobData.length;
          i++
        )
          {
            "year": selfIdentificationController.last5YearJobData[i].year,
            "startYear":
                selfIdentificationController.last5YearJobData[i].startYear,
            "endYear": selfIdentificationController.last5YearJobData[i].endYear,
            "company": selfIdentificationController.last5YearJobData[i].company,
            "position":
                selfIdentificationController.last5YearJobData[i].position,
          },
      ],
      "last2YearProductionData":
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      "lastCompanyManPowerData":
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      "rewardInfoData":
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),
      "notes": notesTextController.text,
    };
    log('Final submit data: $data');
    return data;
  }

  Future submitDraft() async {
    // Submit form to API
    setLoading(true);
    api.performDraftRecruitmentFormPublic(
      controllers: this,
      data: getDataToSubmit(),
      code: kReqSaveDraft,
    );
    return true;
  }

  // Submit form for final submission
  Future<bool> submitFormFinal() async {
    // Submit form to API
    setLoading(true);
    api.performSubmitRecruitmentFormPublic(
      controllers: this,
      data: getDataToSubmit(),
      code: kReqSubmitRecruitmentForm,
    );
    return true;
  }

  // Delegate image picking to verification controller
  Future<void> pickKtpImage(String title, String type) async {
    await verificationController.pickKtpImage(title, type);
  }

  // Delegate image clearing to verification controller
  void clearImage(String type) {
    verificationController.clearImage(type);
  }

  // Delegate KTP OCR processing to identification controller
  Future<void> processKtpOcr(file) async {
    await identificationController.processKtpOcr(file);
  }

  // Check internet connection specifically for web platform
  checkWebInternetConnection() async {
    if (!kIsWeb) return true; // Skip check for non-web platforms

    try {
      setLoading(true);
      api.getComboCategory(controllers: this, code: 1);
    } catch (e) {
      log('Internet connection check failed: $e');
      return false;
    }
  }

  // Validate current page before navigation
  validateCurrentPage({bool? webInternetConnection}) async {
    // Check internet connection for web platform
    if (webInternetConnection == false) {
      Get.snackbar(
        'Tidak Ada Koneksi',
        'Koneksi internet diperlukan untuk melanjutkan. Silakan periksa koneksi Anda.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorGlobalRed,
        margin: EdgeInsets.only(
          bottom: paddingMedium,
          left: paddingMedium,
          right: paddingMedium,
        ),
        duration: const Duration(seconds: 3),
      );
      return;
    }

    bool isValid = false;

    switch (activePage.value) {
      case 0:
        isValid = verificationController.validateForm();
      case 1:
        isValid = identificationController.validateForm();
      case 2:
        isValid = selfIdentificationController.validateForm();
      case 3:
        isValid = termsController.validateForm();
      default:
        isValid = true;
    }
    if (webInternetConnection != null) {
      if (isValid) {
        pageController.nextPage(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        activePage.value += 1;
        if (activePage.value == 1) {
          // Show notification to prompt user to double-check OCR data
          Utils.autoCloseDialog(
            body: 'ktp_check_info_str'.tr,
            type: kPopupInfo,
          );
        }
      } else {
        Utils.popup(body: 'form_mandatory_message_str'.tr, type: kPopupFailed);
      }
    } else {
      if (isValid) {
        // Submit form for final submission
        isReadyToSubmit.value = true;
      } else {
        Utils.popup(body: 'form_mandatory_message_str'.tr, type: kPopupFailed);
      }
    }
  }

  // Navigation methods
  void nextPage() {
    if (activePage.value < 3) {
      activePage.value++;
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousPage() {
    if (activePage.value > 0) {
      activePage.value--;
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page <= 3) {
      activePage.value = page;
      pageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Fungsi untuk disable screenshot
  Future<void> _disableScreenshot() async {
    try {
      bool result = await _noScreenshot.screenshotOff();
      log('Screenshot disabled: $result');
    } catch (e) {
      log('Error disabling screenshot: $e');
    }
  }

  // Fungsi untuk enable screenshot
  Future<void> _enableScreenshot() async {
    try {
      bool result = await _noScreenshot.screenshotOn();
      log('Screenshot enabled: $result');
    } catch (e) {
      log('Error enabling screenshot: $e');
    }
  }

  @override
  void onClose() {
    // Enable kembali screenshot saat keluar dari halaman
    _enableScreenshot();

    // Batalkan timer auto-save
    _autoSaveTimer?.cancel();

    // Dispose page controller
    pageController.dispose();

    // Dispose sub-controllers
    Get.delete<PublicFormVerificationController>();
    Get.delete<PublicFormIdentificationController>();
    Get.delete<PublicFormSelfIdentificationController>();
    Get.delete<PublicFormTermsController>();

    super.onClose();
  }
}
