# Testing Guide: Enhanced Scroll Detection

## 🧪 Manual Testing Scenarios

Setelah implementasi yang diperbaiki, berikut adalah skenario testing yang bisa Anda lakukan untuk memvalidasi deteksi scroll:

### 1. **Mouse Wheel Scroll Test**
**Langkah:**
1. Buka halaman dengan WebView (PKAJ document)
2. Scroll menggunakan mouse wheel di dalam iframe
3. Perhatikan console logs

**Expected Logs:**
```
Setting up web scroll listener for: [URL]
Iframe loaded, attempting scroll detection
Cross-origin iframe detected
Setting up cross-origin scroll detection
Mouse entered iframe (count: 1)
Wheel event detected on iframe
Scroll activity detected: wheel (count: 1)
Wheel event detected on iframe
Scroll activity detected: wheel (count: 2)
Scroll to bottom detected: activity-based detection after 2 events
```

**Expected Time:** 1.5-2 detik setelah berhenti scroll

### 2. **Touch Scroll Test (Mobile/Touch Device)**
**Langkah:**
1. Buka di mobile browser atau gunakan Chrome DevTools touch simulation
2. Scroll menggunakan touch di dalam iframe
3. Perhatikan console logs

**Expected Logs:**
```
Touch start detected on iframe
Scroll activity detected: touchstart (count: 1)
Touch move detected on iframe
Scroll activity detected: touchmove (count: 2)
Scroll to bottom detected: activity-based detection after 2 events
```

### 3. **Keyboard Navigation Test**
**Langkah:**
1. Klik di dalam iframe untuk focus
2. Gunakan arrow keys, Page Up/Down, atau Space untuk navigate
3. Perhatikan console logs

**Expected Logs:**
```
Iframe focused (interaction count: 1)
Key pressed: 40
Scroll activity detected: keyboard-40 (count: 1)
Key pressed: 40
Scroll activity detected: keyboard-40 (count: 2)
Scroll to bottom detected: activity-based detection after 2 events
```

### 4. **Focus/Blur Pattern Test**
**Langkah:**
1. Klik di dalam iframe (focus)
2. Tunggu 2-3 detik
3. Klik di luar iframe (blur)

**Expected Logs:**
```
Iframe focused (interaction count: 1)
Scroll to bottom detected: focus-based quick detection
```
**OR**
```
Iframe focused (interaction count: 1)
Scroll to bottom detected: focus/blur pattern detection
```

**Expected Time:** 0.8-2 detik setelah blur

### 5. **Mouse Movement Pattern Test**
**Langkah:**
1. Hover mouse ke dalam iframe
2. Gerakkan mouse di dalam iframe beberapa kali
3. Pindahkan mouse keluar dari iframe

**Expected Logs:**
```
Mouse entered iframe (count: 1)
Mouse entered iframe (count: 2)
Mouse entered iframe (count: 3)
Mouse entered iframe (count: 4)
Mouse entered iframe (count: 5)
Mouse entered iframe (count: 6)
Scroll to bottom detected: mouse movement pattern detection
```

**Expected Time:** 2.5 detik setelah mouse movement

### 6. **Mouse Enter/Leave Test**
**Langkah:**
1. Hover mouse ke dalam iframe
2. Tunggu 3+ detik di dalam iframe
3. Pindahkan mouse keluar dari iframe

**Expected Logs:**
```
Mouse entered iframe (count: 1)
Scroll to bottom detected: mouse interaction pattern (3000ms+ in iframe)
```

**Expected Time:** 0.5 detik setelah mouse leave (jika >3 detik di dalam)

### 7. **Click Interaction Test**
**Langkah:**
1. Klik di dalam iframe beberapa kali
2. Perhatikan console logs

**Expected Logs:**
```
Click detected on iframe
Scroll activity detected: click (count: 1)
Click detected on iframe
Scroll activity detected: click (count: 2)
Scroll to bottom detected: activity-based detection after 2 events
```

### 8. **No Interaction Test (Fallback)**
**Langkah:**
1. Buka halaman dengan WebView
2. Jangan berinteraksi sama sekali
3. Tunggu

**Expected Logs:**
```
Setting up web scroll listener for: [URL]
Iframe loaded, attempting scroll detection
Cross-origin iframe detected
Setting up cross-origin scroll detection
Scroll to bottom detected: fallback timer (4s)
```

**Expected Time:** Tepat 4 detik

## 🔍 Debug Console Commands

Untuk testing lebih detail, Anda bisa menjalankan commands ini di browser console:

### 1. **Simulate Wheel Event**
```javascript
// Simulate wheel event on iframe
const iframe = document.querySelector('iframe');
const wheelEvent = new WheelEvent('wheel', { deltaY: 100 });
iframe.dispatchEvent(wheelEvent);
```

### 2. **Simulate Touch Events**
```javascript
// Simulate touch events
const iframe = document.querySelector('iframe');
const touchStart = new TouchEvent('touchstart');
const touchMove = new TouchEvent('touchmove');
iframe.dispatchEvent(touchStart);
iframe.dispatchEvent(touchMove);
```

### 3. **Simulate Keyboard Events**
```javascript
// Simulate keyboard navigation
const iframe = document.querySelector('iframe');
const keyEvent = new KeyboardEvent('keydown', { keyCode: 40 }); // Arrow down
iframe.dispatchEvent(keyEvent);
```

### 4. **Check Detection State**
```javascript
// Check if detection has been triggered (this won't work directly, but you can monitor logs)
console.log('Check console logs for detection status');
```

## 📊 Expected Performance

| Test Scenario | Expected Detection Time | Method |
|---------------|------------------------|---------|
| **Mouse wheel scroll** | 1.5-2 seconds | Activity-based |
| **Touch scroll** | 1.5-2 seconds | Activity-based |
| **Keyboard navigation** | 1.5-2 seconds | Activity-based |
| **Focus + quick blur** | 0.8-1 second | Focus/blur pattern |
| **Focus + 2s delay** | 2 seconds | Focus-based quick |
| **Mouse movement (5+ moves)** | 2.5 seconds | Mouse movement pattern |
| **Mouse enter/leave (3s+)** | 0.5 seconds | Mouse interaction |
| **Mouse enter/leave (<3s)** | 1.2 seconds | Mouse interaction |
| **Click interactions** | 1.5-2 seconds | Activity-based |
| **No interaction** | 4 seconds | Fallback timer |

## ✅ Success Criteria

**Test dianggap berhasil jika:**
1. ✅ Console logs muncul sesuai expected
2. ✅ Detection time sesuai dengan range yang diharapkan
3. ✅ Tombol "Sudah Mengerti" aktif setelah detection
4. ✅ Tidak ada error di console
5. ✅ Behavior konsisten di berbagai browser

## 🚨 Troubleshooting

### Jika masih menggunakan fallback timer:
1. **Check console logs** - Pastikan event listeners terpasang
2. **Try different interactions** - Coba berbagai jenis interaksi
3. **Check browser compatibility** - Pastikan browser support event yang digunakan
4. **Verify iframe focus** - Pastikan iframe bisa menerima focus

### Jika detection terlalu cepat:
1. **Increase thresholds** - Naikkan `scrollActivity >= 2` menjadi `>= 3`
2. **Increase delays** - Naikkan timer delays
3. **Add more conditions** - Tambah kondisi untuk detection

### Jika detection terlalu lambat:
1. **Decrease thresholds** - Turunkan activity thresholds
2. **Decrease delays** - Kurangi timer delays
3. **Add more event types** - Tambah event listeners

---

**Silakan test skenario-skenario di atas dan beri tahu hasilnya. Jika ada yang tidak sesuai expected, kita bisa fine-tune implementasinya lebih lanjut!**
