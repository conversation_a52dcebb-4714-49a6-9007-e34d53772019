// ignore_for_file: constant_identifier_names, constant_pattern_never_matches_value_type

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';

enum StatusApproval {
  BARU,
  TERTUNDA,
  MENUNGGU_PERSETUJUAN,
  DISETUJUI,
  DITOLAK,
  DIBATALKAN,
}

enum Status {
  DRAFT,
  IN_PROGRESS,
  COMPLETE,
  EXPIRED,
  REJECTED,
  CANCELLED,
  DIKEMBALIKAN,
}

class StatusLabel extends StatelessWidget {
  const StatusLabel({
    super.key,
    required this.status,
    this.isCancelable = true,
  });
  final String status;
  final bool isCancelable;

  String? _parseStatus(String statusStr) {
    try {
      return Status.values
          .firstWhere((status) => status.name == statusStr)
          .name;
    } catch (e) {
      try {
        return StatusApproval.values
            .firstWhere((status) => status.name == statusStr)
            .name;
      } catch (e) {
        return null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Color bgColor, textColor;
    String text = '';

    final statusEnum = _parseStatus(status);

    if (statusEnum == Status.COMPLETE.name) {
      bgColor = kColorGlobalBgGreen;
      textColor = kColorGlobalGreen;
      text = 'disetujui_str'.tr;
    } else if (statusEnum == Status.CANCELLED.name) {
      bgColor = kColorGlobalBgRed;
      textColor = kColorGlobalRed;
      text = 'trx_stat_cancelled_str'.tr;
    } else if (statusEnum == Status.DIKEMBALIKAN.name) {
      bgColor = kColorGlobalBgWarning;
      textColor = kColorGlobalWarning;
      text = 'trx_stat_returned_str'.tr;
    } else if (statusEnum == Status.REJECTED.name ||
        statusEnum == StatusApproval.DITOLAK.name) {
      bgColor = kColorGlobalBgRed;
      textColor = kColorGlobalRed;
      text = 'ditolak_str'.tr;
    } else if (statusEnum == Status.EXPIRED.name) {
      bgColor = kLine;
      textColor = kColorTextLight;
      text = 'expired_str'.tr;
    } else if (statusEnum == Status.IN_PROGRESS.name) {
      if (isCancelable) {
        bgColor = kColorGlobalBgBlue;
        textColor = kColorPaninBlue;
      } else {
        bgColor = kColorGlobalBgWarning;
        textColor = kColorGlobalWarning;
      }
      text = 'on_progress'.tr;
    } else {
      bgColor = kColorGlobalBgBlue;
      textColor = kColorPaninBlue;
      text = status;
    }

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(paddingExtraSmall),
      child: Text(
        text.toCapitalize(),
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: textColor),
      ),
    );
  }
}
