import 'dart:typed_data';
import 'dart:async';
// this page always open in browser
// ignore: deprecated_member_use, avoid_web_libraries_in_flutter
import 'dart:html' as html;
// ignore: avoid_web_libraries_in_flutter
import 'dart:ui_web' as ui_web;
// ignore: depend_on_referenced_packages
import 'package:flutter_web_plugins/flutter_web_plugins.dart'; // Import for URL strategy
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

void useWebFeature() {
  setUrlStrategy(PathUrlStrategy());
}

Future<Uint8List?> blobUrlToBytes(String blobUrl) async {
  try {
    final response = await html.HttpRequest.request(
      blobUrl,
      responseType: 'arraybuffer',
    );

    if (response.response != null && response.response is ByteBuffer) {
      final byteBuffer = response.response as ByteBuffer;
      return Uint8List.view(byteBuffer);
    }
  } catch (e) {
    // print("Gagal convert blob ke bytes: $e");
  }

  return null;
}

// WebView functions for web platform
String createWebViewIframe(String url, {Function(bool)? onScrolledToBottom}) {
  // Create unique view type for this iframe
  final iframeViewType = 'iframe-${DateTime.now().millisecondsSinceEpoch}';

  // Create wrapper div for better control
  final wrapperDiv =
      html.DivElement()
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.position = 'relative'
        ..style.overflow = 'hidden';

  // Create iframe element with scroll detection wrapper
  final iframe = _createScrollDetectableIframe(url, onScrolledToBottom);
  wrapperDiv.append(iframe);

  // Register the wrapper as a platform view
  ui_web.platformViewRegistry.registerViewFactory(
    iframeViewType,
    (int viewId) => wrapperDiv,
  );

  return iframeViewType;
}

html.IFrameElement _createScrollDetectableIframe(
  String url,
  Function(bool)? onScrolledToBottom,
) {
  String iframeSrc;

  if (onScrolledToBottom != null) {
    // Use our scroll detector wrapper
    final encodedUrl = Uri.encodeComponent(url);
    iframeSrc = 'scroll_detector.html?url=$encodedUrl';
  } else {
    // Direct URL if no scroll detection needed
    iframeSrc = url;
  }

  final iframe =
      html.IFrameElement()
        ..src = iframeSrc
        ..style.border = 'none'
        ..style.width = '100%'
        ..style.height = '100%'
        ..allowFullscreen = true;

  // Add scroll detection if callback is provided
  if (onScrolledToBottom != null) {
    _addWebScrollListener(iframe, onScrolledToBottom);
  }

  return iframe;
}

void _addWebScrollListener(
  html.IFrameElement iframe,
  Function(bool) onScrolledToBottom,
) {
  bool hasScrolledToBottom = false;

  // Listen for messages from our scroll detector iframe
  late html.EventListener messageListener;
  messageListener = (html.Event event) {
    if (event is html.MessageEvent) {
      final data = event.data;
      if (data is Map && data['type'] == 'scroll_position') {
        final isAtBottom = data['isAtBottom'] as bool? ?? false;
        if (isAtBottom && !hasScrolledToBottom) {
          hasScrolledToBottom = true;
          onScrolledToBottom(true);
          // Remove listener after first detection
          html.window.removeEventListener('message', messageListener);

          if (kDebugMode) {
            print('Scroll to bottom detected via postMessage');
          }
        }
      }
    }
  };

  html.window.addEventListener('message', messageListener);

  // Fallback timer (12 seconds to allow for the HTML wrapper's own fallback)
  Timer(Duration(seconds: 12), () {
    if (!hasScrolledToBottom) {
      hasScrolledToBottom = true;
      onScrolledToBottom(true);
      html.window.removeEventListener('message', messageListener);

      if (kDebugMode) {
        print('Scroll to bottom triggered by fallback timer');
      }
    }
  });
}

Widget createWebViewWidget(String? iframeViewType) {
  if (iframeViewType != null) {
    return HtmlElementView(viewType: iframeViewType);
  }
  return const Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(),
        SizedBox(height: 16),
        Text('Loading web content...'),
      ],
    ),
  );
}

// Platform detection functions for web platform
bool get isIOS => false; // Web is never iOS
bool get isAndroid => false; // Web is never Android

// Tesseract OCR functions for web platform
Future<String?> performTesseractOCR(String base64Image) async {
  try {
    if (kDebugMode) {
      print('🚀 Starting Tesseract OCR with Indonesian KTP trained data...');
    }

    // Create a completer to handle the async response
    final completer = Completer<String?>();

    // Create iframe element pointing to tesseract worker
    // Use Indonesian version with fallback to English
    final iframe =
        html.IFrameElement()
          ..src = 'tesseract_worker_indonesian.html'
          ..style.display = 'none'
          ..style.width = '0'
          ..style.height = '0';

    // Add message listener for OCR results and errors
    late html.EventListener messageListener;
    messageListener = (html.Event event) {
      if (event is html.MessageEvent) {
        final data = event.data;
        if (data is Map) {
          if (data['type'] == 'ocr_result') {
            final result = data['data'];
            if (result['success'] == true) {
              if (kDebugMode) {
                print('✅ Tesseract OCR completed successfully');
              }
              completer.complete(result['text'] as String?);
            } else {
              if (kDebugMode) {
                print('❌ Tesseract OCR failed: ${result['error']}');
              }
              completer.complete(null);
            }
            // Clean up
            html.window.removeEventListener('message', messageListener);
            iframe.remove();
          } else if (data['type'] == 'tesseract_error') {
            if (kDebugMode) {
              print(
                '❌ Tesseract initialization error: ${data['data']['error']}',
              );
            }
            completer.complete(null);
            // Clean up
            html.window.removeEventListener('message', messageListener);
            iframe.remove();
          } else if (data['type'] == 'tesseract_progress') {
            if (kDebugMode) {
              final progress = data['data'];
              print(
                '📊 Tesseract progress: ${progress['status']} ${progress['progress'] ?? ''}',
              );
            }
          }
        }
      }
    };

    // Add event listener
    html.window.addEventListener('message', messageListener);

    // Add iframe to document
    html.document.body?.append(iframe);

    // Wait for iframe to load, then send OCR request
    iframe.onLoad.listen((_) {
      // Send OCR request to iframe
      iframe.contentWindow?.postMessage({
        'type': 'perform_ocr',
        'image': base64Image,
      }, '*');
    });

    // Set timeout for OCR operation (30 seconds)
    Timer(const Duration(seconds: 30), () {
      if (!completer.isCompleted) {
        if (kDebugMode) {
          print('⏰ Tesseract OCR timeout');
        }
        completer.complete(null);
        html.window.removeEventListener('message', messageListener);
        iframe.remove();
      }
    });

    return await completer.future;
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error in performTesseractOCR: $e');
    }
    return null;
  }
}
