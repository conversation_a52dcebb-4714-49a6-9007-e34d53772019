<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll Detector</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        #content-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }
        
        .scroll-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }
        
        .scroll-indicator {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-family: Arial, sans-serif;
            display: none;
        }
    </style>
</head>
<body>
    <div class="scroll-overlay">
        <div class="scroll-indicator" id="scrollIndicator">
            Scroll to continue...
        </div>
    </div>
    <iframe id="content-iframe" src="" allowfullscreen></iframe>

    <script>
        (function() {
            let hasReachedBottom = false;
            let contentUrl = '';
            let checkInterval;
            
            // Get URL parameter
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }
            
            // Initialize
            function init() {
                contentUrl = getUrlParameter('url');
                if (contentUrl) {
                    document.getElementById('content-iframe').src = contentUrl;
                    startScrollDetection();
                } else {
                    console.error('No URL parameter provided');
                }
            }
            
            function startScrollDetection() {
                const iframe = document.getElementById('content-iframe');
                const scrollIndicator = document.getElementById('scrollIndicator');
                
                // Show scroll indicator initially
                scrollIndicator.style.display = 'block';
                
                // Try to detect scroll in iframe content
                iframe.onload = function() {
                    try {
                        // Try to access iframe content (will fail for cross-origin)
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        if (iframeDoc) {
                            // Same-origin: we can detect scroll directly
                            setupDirectScrollDetection(iframeDoc, iframe.contentWindow);
                        } else {
                            // Cross-origin: use alternative methods
                            setupCrossOriginScrollDetection(iframe);
                        }
                    } catch (e) {
                        // Cross-origin: use alternative methods
                        setupCrossOriginScrollDetection(iframe);
                    }
                };
            }
            
            function setupDirectScrollDetection(iframeDoc, iframeWindow) {
                function checkScrollPosition() {
                    if (hasReachedBottom) return;
                    
                    const scrollTop = iframeWindow.pageYOffset || iframeDoc.documentElement.scrollTop || iframeDoc.body.scrollTop || 0;
                    const windowHeight = iframeWindow.innerHeight || iframeDoc.documentElement.clientHeight || iframeDoc.body.clientHeight || 0;
                    const documentHeight = Math.max(
                        iframeDoc.body.scrollHeight || 0,
                        iframeDoc.body.offsetHeight || 0,
                        iframeDoc.documentElement.clientHeight || 0,
                        iframeDoc.documentElement.scrollHeight || 0,
                        iframeDoc.documentElement.offsetHeight || 0
                    );
                    
                    // Check if user has scrolled to bottom (with 50px tolerance)
                    const isAtBottom = scrollTop + windowHeight >= documentHeight - 50;
                    
                    // Also check if content is short enough that no scrolling is needed
                    const isContentShort = documentHeight <= windowHeight + 10;
                    
                    if (isAtBottom || isContentShort) {
                        markAsScrolledToBottom();
                    }
                }
                
                // Add scroll listener
                iframeWindow.addEventListener('scroll', checkScrollPosition, { passive: true });
                iframeWindow.addEventListener('resize', checkScrollPosition, { passive: true });
                
                // Check initially
                setTimeout(checkScrollPosition, 500);
                setTimeout(checkScrollPosition, 2000);
                
                // Check when content loads
                if (iframeDoc.readyState === 'loading') {
                    iframeDoc.addEventListener('DOMContentLoaded', () => {
                        setTimeout(checkScrollPosition, 100);
                    });
                } else {
                    setTimeout(checkScrollPosition, 100);
                }
                
                iframeWindow.addEventListener('load', () => {
                    setTimeout(checkScrollPosition, 100);
                });
            }
            
            function setupCrossOriginScrollDetection(iframe) {
                // For cross-origin iframes, we'll use a combination of:
                // 1. Mouse wheel detection
                // 2. Touch events
                // 3. Keyboard events
                // 4. Timer-based fallback
                
                let scrollActivity = 0;
                let lastActivity = Date.now();
                
                // Listen for scroll-related events on the iframe
                iframe.addEventListener('wheel', handleScrollActivity, { passive: true });
                iframe.addEventListener('touchstart', handleScrollActivity, { passive: true });
                iframe.addEventListener('touchmove', handleScrollActivity, { passive: true });
                iframe.addEventListener('keydown', function(e) {
                    // Arrow keys, page up/down, space, home, end
                    if ([32, 33, 34, 35, 36, 37, 38, 39, 40].includes(e.keyCode)) {
                        handleScrollActivity();
                    }
                }, { passive: true });
                
                function handleScrollActivity() {
                    scrollActivity++;
                    lastActivity = Date.now();
                    
                    // After some scroll activity, assume user has scrolled enough
                    if (scrollActivity >= 5) {
                        setTimeout(() => {
                            if (Date.now() - lastActivity > 2000) { // 2 seconds of inactivity
                                markAsScrolledToBottom();
                            }
                        }, 2000);
                    }
                }
                
                // Fallback timer - enable after 8 seconds
                setTimeout(() => {
                    if (!hasReachedBottom) {
                        markAsScrolledToBottom();
                    }
                }, 8000);
            }
            
            function markAsScrolledToBottom() {
                if (hasReachedBottom) return;
                
                hasReachedBottom = true;
                
                // Hide scroll indicator
                const scrollIndicator = document.getElementById('scrollIndicator');
                scrollIndicator.style.display = 'none';
                
                // Notify parent window
                window.parent.postMessage({
                    type: 'scroll_position',
                    isAtBottom: true,
                    timestamp: Date.now()
                }, '*');
                
                console.log('Scroll to bottom detected');
            }
            
            // Start when page loads
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
            } else {
                init();
            }
        })();
    </script>
</body>
</html>
