import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class FormSelfIdentification extends StatelessWidget {
  final FormSelfIdentificationController controller;

  const FormSelfIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Kelengkapan Data Pribadi'),
        Text(
          'Mohon mengisi data dengan benar, tanpa kesalahan.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        SizedBox(height: paddingMedium),
        _infoContact(context),
        SizedBox(height: paddingMedium),
        _emergencyContact(context),
        SizedBox(height: paddingMedium),
        if (controller
                .baseController
                .verificationController
                .candidateLevelController
                .text !=
            'BP')
          _leaderSection(context),
        if (controller
                .baseController
                .verificationController
                .candidateLevelController
                .text !=
            'BP')
          SizedBox(height: paddingMedium),
        _bankSection(context),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Column _leaderSection(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleSecondary(title: 'Pekerjaan sebelumnya'),
        _padding(
          Obx(
            () => PdlDropDown(
              item: [
                kLastJobSalesIssurance,
                kLastJobSalesNotIssurance,
                kLastJobNotSales,
              ],
              selectedItem: controller.lastJob.value,
              title: 'Riwayat pekerjaan',
              enabled: !controller.baseController.isFormDisabled.value,
              onChanged: (val) {
                controller.lastJob.value = val ?? kLastJobSalesIssurance;
              },
              disableSearch: true,
              hasError: controller.lastJobError.value.isNotEmpty,
              errorText:
                  controller.lastJobError.value.isEmpty
                      ? null
                      : controller.lastJobError.value,
            ),
          ),
        ),

        Obx(
          () => _leaderForm(
            context,
            title: 'Riwayat Pekerjaan 5 Tahun Terakhir',
            subtitle: 'Masukkan riwayat pekerjaan 5 tahun terakhir Anda',
            onAdd: () => _work5YearDialog(context),
            isDisabled: controller.baseController.isFormDisabled.value,
            items: [
              for (int i = 0; i < controller.last5YearJobData.length; i++)
                _lastJobCard(controller.last5YearJobData[i], context),
            ],
            errorText:
                controller.last5YearJobDataError.value.isEmpty
                    ? null
                    : controller.last5YearJobDataError.value,
          ),
        ),
        Obx(
          () => _leaderForm(
            context,
            title: 'Produksi Dalam 2 Tahun Terakhir',
            isDisabled: controller.baseController.isFormDisabled.value,
            subtitle: 'Masukkan riwayat produksi 2 tahun terakhir Anda.',
            onAdd: () => _last2YearDialog(context),
            isOptional: true,
            items: [
              for (
                int i = 0;
                i < controller.last2YearProductionData.length;
                i++
              )
                _last2YearCard(context, controller.last2YearProductionData[i]),
            ],
            errorText:
                controller.last2YearProductionDataError.value.isEmpty
                    ? null
                    : controller.last2YearProductionDataError.value,
          ),
        ),

        Obx(
          () => _leaderForm(
            context,
            title: 'Jumlah Man Power di Perusahaan Terakhir',
            subtitle: 'Masukkan jumlah man power',

            onAdd: () => _manPowerDialog(context),
            isOptional: true,
            isDisabled:
                controller.baseController.isFormDisabled.value == false
                    ? controller.lastCompanyManPowerData.value.agentCount != 0
                        ? true
                        : false
                    : true,
            items: [
              if (controller.lastCompanyManPowerData.value.agentCount != 0)
                _lastManPowerCard(
                  context,
                  controller.lastCompanyManPowerData.value,
                ),
            ],
            errorText:
                controller.lastCompanyManPowerDataError.value.isEmpty
                    ? null
                    : controller.lastCompanyManPowerDataError.value,
          ),
        ),
        Obx(
          () => _leaderForm(
            context,
            title: 'Penghargaan Yang Pernah Diterima',
            subtitle: '',
            isDisabled: controller.baseController.isFormDisabled.value,
            isOptional: true,
            onAdd: () => _rewardDialog(context),
            items: [
              for (int i = 0; i < controller.rewardInfoData.length; i++)
                _rewardCard(context, controller.rewardInfoData[i]),
            ],
            errorText: null, // rewardInfoData is not required
          ),
        ),
      ],
    );
  }

  _rewardCard(context, RewardInfoData data) {
    return _leaderCardContainer(
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.description ?? '',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
                Text(
                  '${data.year}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          if (controller.baseController.isFormDisabled.value != true)
            GestureDetector(
              onTap: () {
                controller.rewardInfoData.remove(data);
              },
              child: Container(
                color: Colors.transparent,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-trash-bin.svg',
                  width: 25,
                  color: kColorGlobalRed,
                ),
              ),
            ),
        ],
      ),
    );
  }

  PdlBaseDialog _rewardDialog(context) {
    TextEditingController rewardNameController = TextEditingController();
    TextEditingController rewardYearController = TextEditingController();

    return PdlBaseDialog(
      context: context,
      child: _leaderDialogContent(
        title: 'Penghargaan',
        children: [
          _padding(
            PdlTextField(
              label: 'Nama Penghargaan',
              hint: 'Masukkan nama penghargaan',
              textController: rewardNameController,
              isCapslockOn: true,
            ),
          ),
          _padding(
            PdlDropDown(
              item: ['Pilih', ...controller.getYearsListToToday(yearsAgo: 50)],
              selectedItem: 'Pilih',
              title: 'Tahun',
              enabled: true,
              onChanged: (val) {
                rewardYearController.text = val ?? '';
              },
              disableSearch: true,
            ),
          ),
        ],
        onSave: () {
          if (rewardNameController.text.isEmpty ||
              rewardYearController.text.isEmpty) {
            Get.snackbar(
              'Peringatan',
              'Silakan isi nama penghargaan dan tahun',
              backgroundColor: kColorGlobalBgRed,
              colorText: kColorGlobalRed,
              snackPosition: SnackPosition.BOTTOM,
            );
            return;
          }
          RewardInfoData item = RewardInfoData(
            year: int.tryParse(rewardYearController.text) ?? 0,
            description: rewardNameController.text,
          );
          controller.rewardInfoData.add(item);
          Get.back();
        },
      ),
    );
  }

  PdlBaseDialog _manPowerDialog(context) {
    TextEditingController agentCountController = TextEditingController();
    TextEditingController leaderCountController = TextEditingController();

    return PdlBaseDialog(
      context: context,
      child: _leaderDialogContent(
        title: 'Jumlah Man Power',
        children: [
          _padding(
            PdlTextField(
              label: 'Jumlah Agen',
              hint: '0',
              suffixHelperText: 'Orang',
              textController: agentCountController,
              keyboardType: TextInputType.numberWithOptions(),
            ),
          ),
          _padding(
            PdlTextField(
              label: 'Jumlah Leader',
              hint: '0',
              suffixHelperText: 'Orang',
              textController: leaderCountController,
              keyboardType: TextInputType.numberWithOptions(),
            ),
          ),
        ],
        onSave: () {
          LastCompanyManPowerData item = LastCompanyManPowerData(
            agentCount: int.tryParse(agentCountController.text) ?? 0,
            leaderCount: int.tryParse(leaderCountController.text) ?? 0,
          );
          controller.lastCompanyManPowerData.value = item;
          Get.back();
        },
      ),
    );
  }

  PdlBaseDialog _last2YearDialog(context) {
    TextEditingController pribadiTextController = TextEditingController();
    TextEditingController teamTextController = TextEditingController();

    return PdlBaseDialog(
      context: context,
      child: _leaderDialogContent(
        title: 'Produksi 2 Tahun Terakhir',
        children: [
          _padding(
            PdlTextField(
              label: 'Pribadi',
              hint: '0',
              prefixHelperText: 'Rp',
              isCurrency: true,
              textController: pribadiTextController,
              keyboardType: TextInputType.numberWithOptions(),
            ),
          ),
          _padding(
            PdlTextField(
              label: 'Total Team',
              hint: '0',
              prefixHelperText: 'Rp',
              isCurrency: true,
              textController: teamTextController,
              keyboardType: TextInputType.numberWithOptions(),
            ),
          ),
        ],
        onSave: () {
          if (pribadiTextController.text.isEmpty ||
              teamTextController.text.isEmpty) {
            Get.snackbar(
              'Peringatan',
              'Silakan isi produksi pribadi dan total team',
              backgroundColor: kColorGlobalBgRed,
              colorText: kColorGlobalRed,
              snackPosition: SnackPosition.BOTTOM,
            );
            return;
          }
          Last2YearProductionData item = Last2YearProductionData(
            personalProduction: int.tryParse(pribadiTextController.text) ?? 0,
            teamProduction: int.tryParse(teamTextController.text) ?? 0,
          );
          controller.last2YearProductionData.add(item);
          Get.back();
        },
      ),
    );
  }

  Container _last2YearCard(context, Last2YearProductionData data) {
    return _leaderCardContainer(
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Pribadi', style: Theme.of(context).textTheme.bodyMedium),
                Text(
                  Utils.currencyFormatters(
                    data: data.personalProduction.toString(),
                    currency: 'Rp.',
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
                Text(
                  'Total Team',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  Utils.currencyFormatters(
                    data: data.teamProduction.toString(),
                    currency: 'Rp.',
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
              ],
            ),
          ),
          if (controller.baseController.isFormDisabled.value != true)
            GestureDetector(
              onTap: () {
                controller.last2YearProductionData.remove(data);
              },
              child: Container(
                color: Colors.transparent,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-trash-bin.svg',
                  width: 25,
                  color: kColorGlobalRed,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Container _lastManPowerCard(context, LastCompanyManPowerData data) {
    return _leaderCardContainer(
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Agen', style: Theme.of(context).textTheme.bodyMedium),
                Text(
                  Utils.currencyFormatters(
                    data: data.agentCount.toString(),
                    currency: '',
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
                Text('Leader', style: Theme.of(context).textTheme.bodyMedium),
                Text(
                  Utils.currencyFormatters(
                    data: data.leaderCount.toString(),
                    currency: '',
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
              ],
            ),
          ),
          if (controller.baseController.isFormDisabled.value != true)
            GestureDetector(
              onTap: () {
                controller.lastCompanyManPowerData.value =
                    LastCompanyManPowerData(agentCount: 0, leaderCount: 0);
              },
              child: Container(
                color: Colors.transparent,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-trash-bin.svg',
                  width: 25,
                  color: kColorGlobalRed,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Container _lastJobCard(Last5YearJobData data, context) {
    return _leaderCardContainer(
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.company ?? '-',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  data.position ?? '-',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
                if (data.startYear != null)
                  Text(
                    '${data.startYear} - ${data.endYear}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
              ],
            ),
          ),
          if (controller.baseController.isFormDisabled.value != true)
            GestureDetector(
              onTap: () {
                controller.last5YearJobData.remove(data);
              },
              child: Container(
                color: Colors.transparent,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-trash-bin.svg',
                  width: 25,
                  color: kColorGlobalRed,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Container _leaderCardContainer(Widget child) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      margin: EdgeInsets.only(bottom: paddingSmall),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: kColorGlobalBlue, width: 2)),
      ),
      width: Get.width,
      child: child,
    );
  }

  PdlBaseDialog _work5YearDialog(context) {
    TextEditingController companyController = TextEditingController();
    TextEditingController positionController = TextEditingController();
    TextEditingController startYearController = TextEditingController();
    TextEditingController endYearController = TextEditingController();

    RxString startYear = ''.obs;

    return PdlBaseDialog(
      context: context,
      child: _leaderDialogContent(
        title: 'Riwayat Pekerjaan',
        onSave: () {
          if (companyController.text.isEmpty ||
              positionController.text.isEmpty) {
            Get.snackbar(
              'Peringatan',
              'Silakan isi nama perusahaan dan posisi',
              backgroundColor: kColorGlobalBgRed,
              colorText: kColorGlobalRed,
              snackPosition: SnackPosition.BOTTOM,
            );
            return;
          }
          if (startYearController.text.isEmpty ||
              endYearController.text.isEmpty) {
            Get.snackbar(
              'Peringatan',
              'Silakan pilih tahun mulai dan sampai',
              backgroundColor: kColorGlobalBgRed,
              colorText: kColorGlobalRed,
              snackPosition: SnackPosition.BOTTOM,
            );
            return;
          }

          int years =
              int.parse(endYearController.text) -
              int.parse(startYearController.text);
          Last5YearJobData item = Last5YearJobData(
            company: companyController.text.trim(),
            position: positionController.text.trim(),
            year: years,
            startYear: int.parse(startYearController.text),
            endYear: int.parse(endYearController.text),
          );
          controller.last5YearJobData.add(item);

          log('Added job data, total: ${controller.last5YearJobData.length}');
          Get.back();
        },
        children: [
          _padding(
            PdlTextField(
              label: 'Nama Perusahaan',
              hint: 'Masukkan Nama Perusahaan',
              textController: companyController,
              isCapslockOn: true,
            ),
          ),
          _padding(
            PdlTextField(
              label: 'Posisi',
              hint: 'Posisi / jabatan',
              textController: positionController,
              isCapslockOn: true,
            ),
          ),
          _padding(
            Row(
              children: [
                Expanded(
                  child: PdlDropDown(
                    item: [...controller.getYearsListToToday()],
                    title: 'Mulai Tahun',
                    enabled: true,
                    onChanged: (val) {
                      startYear.value = val ?? '';
                      startYearController.text = val ?? '';
                    },
                    disableSearch: true,
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: Obx(
                    () => PdlDropDown(
                      item: [
                        ...controller.getYearsListToToday(
                          startYear:
                              startYear.value.isEmpty
                                  ? null
                                  : int.parse(startYear.value),
                        ),
                      ],
                      selectedItem:
                          endYearController.text != ''
                              ? endYearController.text
                              : null,
                      title: 'Sampai Tahun',
                      enabled: true,
                      onChanged: (val) {
                        endYearController.text = val!;
                      },
                      disableSearch: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Column _leaderDialogContent({
    required String title,
    required List<Widget> children,
    required Function() onSave,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleSecondary(title: title),
        ...children,
        SizedBox(height: paddingMedium),
        Row(
          children: [
            Expanded(
              child: PdlButton(
                title: 'Batal',
                onPressed: () => Get.back(),
                backgroundColor: Colors.transparent,
                foregorundColor: kColorGlobalBlue,
                borderColor: kColorGlobalBlue,
              ),
            ),
            SizedBox(width: paddingMedium),
            Expanded(child: PdlButton(title: 'Simpan', onPressed: onSave)),
          ],
        ),
      ],
    );
  }

  Widget _leaderForm(
    context, {
    required String title,
    required String subtitle,
    required Function() onAdd,
    required List<Widget> items,
    bool? isOptional = false,
    bool? isDisabled = false,
    String? errorText,
  }) {
    return _padding(
      Column(
        children: [
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(child: TitleSecondary(title: title)),
                if (isOptional == true)
                  Container(
                    padding: EdgeInsets.all(paddingExtraSmall),
                    decoration: BoxDecoration(
                      color:
                          Get.isDarkMode
                              ? kColorGlobalBgDarkBlue
                              : kColorBorderLight,
                      borderRadius: BorderRadius.circular(radiusMedium),
                    ),
                    child: Text(
                      'Opsional',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        color:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (items.isEmpty)
            SizedBox(
              width: Get.width,
              child: Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Get.isDarkMode
                          ? kColorTextTersierLight
                          : kColorTextTersier,
                ),
              ),
            ),
          SizedBox(height: paddingSmall),
          ...items,
          if (errorText != null && errorText.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: paddingSmall),
              child: Text(
                errorText,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
              ),
            ),
          if (isDisabled != true)
            PdlButton(
              title: 'Tambah',
              onPressed: onAdd,
              prefixIcon: Icon(Icons.add_circle_outline_rounded),
            ),
        ],
      ),
    );
  }

  Column _infoContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleSecondary(title: 'Informasi & Kontak'),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Email',
              enabled: !controller.baseController.isFormDisabled.value,
              textController: controller.emailController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.emailController,
                  ),
              hasError: controller.emailError.value.isNotEmpty,
              errorText:
                  controller.emailError.value.isEmpty
                      ? null
                      : controller.emailError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor HP',
              enabled: !controller.baseController.isFormDisabled.value,
              isPhoneNumber: true,
              textController: controller.nomorHpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.nomorHpController,
                  ),
              hasError: controller.nomorHpError.value.isNotEmpty,
              errorText:
                  controller.nomorHpError.value.isEmpty
                      ? null
                      : controller.nomorHpError.value,
            ),
          ),
        ),
        _padding(
          Obx(() {
            if (controller.state.value == ControllerState.loading) {
              return CircularProgressIndicator();
            }
            if (controller.occupationList.isEmpty) {
              return Container();
            }
            return PdlDropDown(
              item: [
                for (int i = 0; i < controller.occupationList.length; i++)
                  controller.occupationList[i].value ?? '-',
              ],
              selectedItem:
                  controller.pekerjaanController.text == ''
                      ? null
                      : controller.pekerjaanController.text,
              title: 'Pekerjaan',
              enabled: !controller.baseController.isFormDisabled.value,
              onChanged: (val) {
                controller.pekerjaanController.text = val ?? '';
                controller.pekerjaanCodeController.text = val ?? '';
                controller.pekerjaanCodeController.text =
                    controller.occupationList
                        .firstWhere((item) => item.value == val)
                        .key ??
                    '';
              },
              hasError: controller.pekerjaanError.value.isNotEmpty,
              errorText:
                  controller.pekerjaanError.value.isEmpty
                      ? null
                      : controller.pekerjaanError.value,
            );
          }),
        ),
      ],
    );
  }

  Column _emergencyContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleSecondary(title: 'Kontak Darurat'),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama',
              isCapslockOn: true,
              enabled: !controller.baseController.isFormDisabled.value,
              textController: controller.emergencyNamaController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.emergencyNamaController,
                  ),
              hasError: controller.emergencyNamaError.value.isNotEmpty,
              errorText:
                  controller.emergencyNamaError.value.isEmpty
                      ? null
                      : controller.emergencyNamaError.value,
            ),
          ),
        ),
        _padding(
          PdlDropDown(
            item: controller.emergencyContactStatusList,
            selectedItem:
                controller.emergencyHubunganController.text.isEmpty ||
                        !controller.emergencyContactStatusList.contains(
                          controller.emergencyHubunganController.text,
                        )
                    ? null
                    : controller.emergencyHubunganController.text,
            title: 'Hubungan Dengan Anda',
            enabled: !controller.baseController.isFormDisabled.value,
            onChanged: (val) {
              controller.emergencyHubunganController.text = val!;
            },
            disableSearch: true,
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor HP',
              isPhoneNumber: true,
              enabled: !controller.baseController.isFormDisabled.value,
              textController: controller.emergencyNomorHpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.emergencyNomorHpController,
                  ),
              hasError: controller.emergencyNomorHpError.value.isNotEmpty,
              errorText:
                  controller.emergencyNomorHpError.value.isEmpty
                      ? null
                      : controller.emergencyNomorHpError.value,
            ),
          ),
        ),
      ],
    );
  }

  Column _bankSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleSecondary(title: 'Rekening'),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama Pemilik Rekening',
              isCapslockOn: true,
              enabled: !controller.baseController.isFormDisabled.value,
              textController: controller.namaPemilikRekeningController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.namaPemilikRekeningController,
                  ),
              hasError: controller.namaPemilikRekeningError.value.isNotEmpty,
              errorText:
                  controller.namaPemilikRekeningError.value.isEmpty
                      ? null
                      : controller.namaPemilikRekeningError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor Rekening',
              enabled: !controller.baseController.isFormDisabled.value,
              textController: controller.nomorRekeningController,
              hasError: controller.nomorRekeningError.value.isNotEmpty,
              keyboardType: TextInputType.numberWithOptions(),
              errorText:
                  controller.nomorRekeningError.value.isEmpty
                      ? null
                      : controller.nomorRekeningError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              textController: controller.namaBankController,
              hint: 'Bank',
              label: 'Bank',
              enabled: !controller.baseController.isFormDisabled.value,
              onChanged: (val) => controller.onBankTextChanged(val),
              prefixIcon: Icon(Icons.search),
              hasError: controller.namaBankError.value.isNotEmpty,
              errorText:
                  controller.namaBankError.value.isEmpty
                      ? null
                      : controller.namaBankError.value,
              items: [
                for (int i = 0; i < controller.bankList.length; i++)
                  GestureDetector(
                    onTap: () {
                      controller.namaBankController.text =
                          controller.bankList[i].bankName ?? '-';
                      controller.bankCode.value =
                          controller.bankList[i].id ?? 0;
                      controller.bankList.clear();
                    },
                    child: Container(
                      width: Get.width,
                      color: Colors.transparent,
                      padding: EdgeInsets.only(top: paddingSmall),
                      child: Text(controller.bankList[i].bankName ?? '-'),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
